# 小米车载手势识别项目功能文档

## 项目概述

`com.xiaomi.cariot.gesture` 是一个基于 React Native 开发的小米车载手势识别智能插件，专为车载环境设计，支持手机端和车机端双平台运行，提供手势识别、智能场景绑定、自定义动作等核心功能。

## 项目信息

- **项目名称**: com.xiaomi.cariot.gesture
- **版本**: 1.0.28
- **最低SDK版本**: 10080
- **支持平台**: Android、iOS、车机系统
- **主要用途**: 车载手势识别控制

## 项目结构

### 入口配置
- **手机端入口**: `Main/PhoneIndex.js` - 手机端设置页面
- **车机端入口**: `Main/CarIndex.js` - 车机主控制页面
- **平台判断**: 根据 `Package.entryInfo.mobileType` 自动选择对应入口

### 核心模块

#### 1. 车机主控制模块 (`car/`)
- **MainCarPage.js**: 车机主页面，手势识别核心控制界面

**主要功能**:
- ✅ 手势识别开关控制
- ✅ 6种手势类型支持
- ✅ 手势绑定功能管理
- ✅ 灵敏度调节
- ✅ 温度保护机制
- ✅ 实时状态监控

#### 2. 手机端设置模块 (`phone/`)
- **MHSetting.js**: 手机端设置页面

#### 3. 工具类模块 (`util/`)
- **LogUtil.js**: 日志管理
- **CommonUtil.js**: 通用工具函数

### 支持的手势类型

#### 6种预设手势
1. **Yeah手势** (✌️): 胜利手势
2. **Five手势** (🖐️): 张开五指
3. **ThumbLeft手势** (👈): 左拇指指向
4. **ThumbRight手势** (👉): 右拇指指向  
5. **ThumbUp手势** (👍): 点赞手势
6. **OK手势** (👌): OK手势

### 手势绑定功能

#### 1. 预设功能绑定
- **无功能**: 不执行任何操作
- **播放/暂停**: 控制音乐播放
- **上一首**: 切换到上一首音乐
- **下一首**: 切换到下一首音乐
- **导航**: 打开导航应用
- **拍照**: 执行拍照功能

#### 2. 智能场景绑定
- 支持与小米智能家居场景联动
- 通过 HyperMind 应用配置智能场景
- 支持复杂的条件触发逻辑

#### 3. 自定义动作绑定
- 支持自定义动作配置
- 通过 HyperMind 应用设置自定义行为
- 灵活的动作组合

## 核心功能特性

### 1. 手势识别引擎
- **实时识别**: 毫秒级手势识别响应
- **高精度**: 支持复杂手势识别
- **抗干扰**: 适应车载环境光线变化
- **多角度**: 支持不同角度手势识别

### 2. 智能绑定系统
- **多类型绑定**: 预设、智能场景、自定义三种绑定方式
- **动态配置**: 实时修改手势绑定关系
- **状态同步**: 车机与手机端状态实时同步
- **规则管理**: 支持复杂的绑定规则配置

### 3. 车载优化
- **温度保护**: 高温环境自动保护机制
- **触控优化**: 适配车机触控操作
- **界面简化**: 专为车载环境设计的简洁界面
- **安全模式**: 驾驶时的安全交互设计

### 4. 设备管理
- **版本兼容**: 支持不同固件版本
- **状态监控**: 实时监控设备连接状态
- **错误处理**: 完善的错误处理机制
- **日志记录**: 详细的操作日志记录

## 技术架构

### 前端技术栈
- **React Native**: 跨平台移动应用框架
- **React Navigation**: 页面路由管理
- **MIOT SDK**: 小米IoT设备SDK
- **micariot-ui-sdk**: 车载UI组件库

### 第三方依赖
```json
{
  "micariot-ui-sdk": "file:../../micariot-ui-sdk",
  "patch-package": "^6.4.7",
  "react-native-root-toast": "^3.2.0",
  "react-native-svg-uri": "^1.2.3"
}
```

### 通信协议
- **SPEC协议**: 设备属性和动作控制
- **RPC通信**: 设备远程过程调用
- **URI Scheme**: 与其他应用的深度链接

### 核心API

#### 手势控制API
```javascript
// 获取手势开关状态
Service.spec.getPropertiesValue([{
  did: Device.deviceID,
  siid: 2,
  piid: 1
}])

// 设置手势开关
Service.spec.setPropertiesValue([{
  did: Device.deviceID,
  siid: 2,
  piid: 1,
  value: true/false
}])

// 设置灵敏度
Service.spec.setPropertiesValue([{
  did: Device.deviceID,
  siid: 3,
  piid: 2,
  value: 0-2 // 低中高三档
}])
```

#### 手势绑定API
```javascript
// 获取手势绑定类型
Service.spec.doAction({
  did: car_did,
  miid: 27,
  siid: 1,
  aiid: 7,
  in: [deviceID, JSON.stringify(iid)]
})

// 设置手势绑定
Service.spec.doAction({
  did: car_did,
  miid: 27,
  siid: 1,
  aiid: 8,
  in: [deviceID, bindType, iid]
})
```

## 用户界面

### 车机端界面
- **头部控制区**: 设备名称、总开关、关闭按钮
- **手势选择区**: 6个手势类型的分段控制器
- **功能绑定区**: 预设功能的网格选择界面
- **智能卡片区**: 智能场景和自定义动作卡片
- **灵敏度设置**: 低、中、高三档灵敏度调节

### 手机端界面
- **设置页面**: 基础的设置选项界面

## 开发指南

### 环境要求
- Node.js >= 12.0
- React Native >= 0.60
- MIOT SDK >= 10080

### 安装依赖
```bash
cd com.xiaomi.cariot.gesture
npm install
```

### 启动项目
```bash
npm start
```

### 构建发布
```bash
npm run publish
```

## 配置说明

### 手势IID配置
```javascript
// 手势IID前缀
gesture_iid: "99.1."

// 每个手势占用4个IID
// 手势0: 99.1.1(预设), 99.1.2(智能场景), 99.1.3(自定义), 99.1.4(绑定类型)
// 手势1: 99.1.5(预设), 99.1.6(智能场景), 99.1.7(自定义), 99.1.8(绑定类型)
// ...以此类推
```

### 预设规则ID
```javascript
preset_ruleid: [
  "0",    // 无功能
  "1001", // 播放/暂停
  "1002", // 上一首
  "1003", // 下一首
  "1004", // 导航
  "1005"  // 拍照
]
```

## 版本兼容性

### 固件版本要求
- **开关功能**: 固件版本 >= 3.0.3_0177
- **灵敏度调节**: 固件版本 >= 3.0.3_0199

### 功能支持检测
```javascript
// 检查是否支持开关功能
Device.lastVersion >= CAR_SUPPORT_SWITCH_VERSION

// 检查是否支持灵敏度调节
Device.lastVersion >= CAR_SUPPORT_SENSITIVITY_VERSION
```

## 错误处理

### 温度保护
- 设备温度过高时自动禁用手势识别
- 界面显示温度保护警告
- 所有交互操作被禁用

### 网络异常
- 设备离线时自动重连
- 网络恢复后自动同步状态
- 操作失败时显示友好提示

### 数据异常
- JSON解析失败的容错处理
- 接口返回异常的重试机制
- 状态不一致时的自动修复

## 平台支持
- ✅ Android 车机系统
- ✅ iOS 车机系统
- ✅ Android 手机端
- ✅ iOS 手机端

## 注意事项
1. 手势识别功能需要设备摄像头支持
2. 智能场景绑定需要安装 HyperMind 应用
3. 部分功能需要车机系统权限
4. 温度过高时会自动禁用功能保护设备

---
*最后更新时间: 2024年12月*
